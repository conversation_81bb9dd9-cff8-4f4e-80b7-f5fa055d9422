import React, { useState, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Card,
  Typography,
  Button,
  Tabs,
  Select,
  Space,
  Divider,
  Checkbox,
  Radio,
  message,
  Tooltip,
  Alert,
  Modal,
  Input,
  Form,
  Progress,
  List,
  Tag,
  Collapse,
  Switch,
  Slider,
  Upload,
  Spin
} from 'antd';
import {
  CodeOutlined,
  DownloadOutlined,
  CopyOutlined,
  ExportOutlined,
  FileZipOutlined,
  GithubOutlined,
  CheckOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  HistoryOutlined,
  EyeOutlined,
  DeleteOutlined,
  CloudDownloadOutlined,
  FolderOutlined,
  FileOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import { styled } from '../../design-system';
import theme from '../../design-system/theme';
import { generateCode } from '../../utils/code_generator';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Panel } = Collapse;

const ExporterContainer = styled.div`
  padding: ${theme.spacing[4]};
  max-width: 1200px;
  margin: 0 auto;
`;

const PreviewContainer = styled.div`
  background-color: ${theme.colors.neutral[50]};
  border: 1px solid ${theme.colors.neutral[300]};
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing[3]};
  margin: ${theme.spacing[3]} 0;
  max-height: 600px;
  overflow: auto;
`;

const CodePreview = styled.pre`
  background-color: ${theme.colors.neutral[900]};
  color: ${theme.colors.neutral[100]};
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing[3]};
  overflow: auto;
  max-height: 500px;
  font-family: ${theme.typography.fontFamily.code};
  font-size: ${theme.typography.fontSize.sm};
  line-height: 1.5;
  margin: 0;
`;

const OptionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${theme.spacing[4]};
  margin: ${theme.spacing[4]} 0;
`;

const OptionCard = styled(Card)`
  .ant-card-body {
    padding: ${theme.spacing[3]};
  }
`;

const ExportHistoryItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${theme.spacing[2]};
  border: 1px solid ${theme.colors.neutral[200]};
  border-radius: ${theme.borderRadius.sm};
  margin-bottom: ${theme.spacing[2]};
`;

/**
 * Enhanced Code Exporter Component
 * Provides comprehensive export functionality with multiple frameworks, preview, and advanced options
 */
const EnhancedCodeExporter = () => {
  const dispatch = useDispatch();
  const components = useSelector(state => state.app?.components || []);
  const layouts = useSelector(state => state.app?.layouts || []);
  const activeTheme = useSelector(state => state.themes?.activeTheme || 'default');
  const themes = useSelector(state => state.themes?.themes || []);
  const websocketConnected = useSelector(state => state.websocket?.connected || false);
  const templates = useSelector(state => state.templates || {});

  // Export configuration state
  const [exportFormat, setExportFormat] = useState('react');
  const [exportOptions, setExportOptions] = useState({
    typescript: false,
    includeAccessibility: true,
    includeTests: false,
    includeStorybook: false,
    styleFramework: 'styled-components',
    stateManagement: 'useState',
    projectStructure: 'single-file',
    bundler: 'vite',
    packageManager: 'npm',
    includeDocker: false,
    includeCiCd: false
  });

  // UI state
  const [activeTab, setActiveTab] = useState('configure');
  const [codePreview, setCodePreview] = useState('');
  const [previewLoading, setPreviewLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [exportHistory, setExportHistory] = useState([]);
  const [selectedComponents, setSelectedComponents] = useState([]);
  const [selectedLayouts, setSelectedLayouts] = useState([]);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [batchExportMode, setBatchExportMode] = useState(false);
  const [selectedExports, setSelectedExports] = useState([]);

  // Available export formats
  const exportFormats = [
    { value: 'react', label: 'React', icon: '⚛️', description: 'Modern React with hooks' },
    { value: 'react-ts', label: 'React + TypeScript', icon: '⚛️', description: 'React with TypeScript support' },
    { value: 'vue', label: 'Vue.js', icon: '🟢', description: 'Vue 3 with Composition API' },
    { value: 'vue-ts', label: 'Vue + TypeScript', icon: '🟢', description: 'Vue 3 with TypeScript' },
    { value: 'angular', label: 'Angular', icon: '🔴', description: 'Angular with TypeScript' },
    { value: 'svelte', label: 'Svelte', icon: '🧡', description: 'Svelte with modern features' },
    { value: 'next', label: 'Next.js', icon: '⚫', description: 'Next.js with SSR support' },
    { value: 'nuxt', label: 'Nuxt.js', icon: '🟢', description: 'Nuxt.js for Vue' },
    { value: 'html', label: 'HTML/CSS/JS', icon: '🌐', description: 'Vanilla web technologies' },
    { value: 'react-native', label: 'React Native', icon: '📱', description: 'Mobile app with React Native' },
    { value: 'flutter', label: 'Flutter', icon: '🐦', description: 'Cross-platform with Flutter' },
    { value: 'ionic', label: 'Ionic', icon: '⚡', description: 'Hybrid mobile apps' }
  ];

  // Style frameworks
  const styleFrameworks = [
    { value: 'styled-components', label: 'Styled Components', description: 'CSS-in-JS with styled-components' },
    { value: 'emotion', label: 'Emotion', description: 'CSS-in-JS with Emotion' },
    { value: 'tailwind', label: 'Tailwind CSS', description: 'Utility-first CSS framework' },
    { value: 'css-modules', label: 'CSS Modules', description: 'Scoped CSS with modules' },
    { value: 'material-ui', label: 'Material-UI', description: 'React Material Design components' },
    { value: 'chakra-ui', label: 'Chakra UI', description: 'Simple and modular React components' },
    { value: 'bootstrap', label: 'Bootstrap', description: 'Popular CSS framework' }
  ];

  // State management options
  const stateManagementOptions = [
    { value: 'useState', label: 'React Hooks (useState)', description: 'Built-in React state management' },
    { value: 'redux', label: 'Redux Toolkit', description: 'Predictable state container' },
    { value: 'zustand', label: 'Zustand', description: 'Lightweight state management' },
    { value: 'context', label: 'React Context', description: 'Built-in context API' }
  ];

  // Project structure options
  const projectStructures = [
    { value: 'single-file', label: 'Single File', description: 'All code in one file' },
    { value: 'multi-file', label: 'Multiple Files', description: 'Organized file structure' },
    { value: 'full-project', label: 'Full Project', description: 'Complete project with configs' }
  ];

  // Initialize selected components and layouts
  useEffect(() => {
    if (components.length > 0 && selectedComponents.length === 0) {
      setSelectedComponents(components.map(c => c.id));
    }
    if (layouts.length > 0 && selectedLayouts.length === 0) {
      setSelectedLayouts(layouts.map(l => l.id));
    }
  }, [components, layouts, selectedComponents.length, selectedLayouts.length]);

  // Load export history from localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem('exportHistory');
    if (savedHistory) {
      try {
        setExportHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Failed to load export history:', error);
      }
    }
  }, []);

  // Generate code preview
  const generatePreview = useCallback(async () => {
    setPreviewLoading(true);
    try {
      const appData = {
        components: components.filter(c => selectedComponents.includes(c.id)),
        layouts: layouts.filter(l => selectedLayouts.includes(l.id)),
        styles: {},
        data: {}
      };

      const code = generateCode(appData, exportFormat, exportOptions);
      setCodePreview(typeof code === 'string' ? code : JSON.stringify(code, null, 2));
    } catch (error) {
      console.error('Preview generation failed:', error);
      message.error('Failed to generate preview');
      setCodePreview('// Preview generation failed');
    } finally {
      setPreviewLoading(false);
    }
  }, [components, layouts, selectedComponents, selectedLayouts, exportFormat, exportOptions]);

  // Auto-generate preview when options change
  useEffect(() => {
    if (activeTab === 'preview') {
      generatePreview();
    }
  }, [activeTab, generatePreview]);

  // WebSocket integration for real-time updates
  useEffect(() => {
    if (websocketConnected && activeTab === 'preview') {
      // Send export configuration to WebSocket for real-time updates
      const wsMessage = {
        type: 'export_config_update',
        data: {
          format: exportFormat,
          options: exportOptions,
          components: selectedComponents,
          layouts: selectedLayouts
        }
      };

      // This would be handled by the WebSocket service
      console.log('WebSocket export config update:', wsMessage);
    }
  }, [websocketConnected, activeTab, exportFormat, exportOptions, selectedComponents, selectedLayouts]);

  // Handle export option changes
  const handleOptionChange = (key, value) => {
    setExportOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Handle enhanced export
  const handleEnhancedExport = async () => {
    setExportLoading(true);
    setExportProgress(0);

    try {
      const appData = {
        components: components.filter(c => selectedComponents.includes(c.id)),
        layouts: layouts.filter(l => selectedLayouts.includes(l.id)),
        styles: {},
        data: {},
        // Include template information for enhanced export
        layout_templates: templates.layoutTemplates || [],
        app_templates: templates.appTemplates || [],
        active_theme: activeTheme,
        theme_data: themes.find(t => t.id === activeTheme) || {}
      };

      // Simulate progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await fetch('/api/enhanced-export/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          app_id: 1, // Replace with actual app ID
          format: exportFormat,
          options: exportOptions
        })
      });

      clearInterval(progressInterval);
      setExportProgress(100);

      if (!response.ok) {
        throw new Error('Export failed');
      }

      const result = await response.json();

      // Save to export history
      const exportRecord = {
        id: Date.now(),
        format: exportFormat,
        options: exportOptions,
        timestamp: new Date().toISOString(),
        status: 'success',
        type: result.type,
        size: result.code ? result.code.length : Object.keys(result.files || {}).length
      };

      const newHistory = [exportRecord, ...exportHistory.slice(0, 9)]; // Keep last 10
      setExportHistory(newHistory);
      localStorage.setItem('exportHistory', JSON.stringify(newHistory));

      // Handle download based on result type
      if (result.type === 'single-file') {
        downloadFile(result.code, `app.${getFileExtension(exportFormat)}`);
      } else if (result.type === 'multi-file') {
        downloadMultipleFiles(result.files);
      } else if (result.type === 'zip') {
        downloadZipFile(result.zip_data, 'app-export.zip');
      }

      message.success('Export completed successfully!');

    } catch (error) {
      console.error('Export failed:', error);
      message.error('Export failed. Please try again.');

      // Save failed export to history
      const exportRecord = {
        id: Date.now(),
        format: exportFormat,
        options: exportOptions,
        timestamp: new Date().toISOString(),
        status: 'failed',
        error: error.message
      };

      const newHistory = [exportRecord, ...exportHistory.slice(0, 9)];
      setExportHistory(newHistory);
      localStorage.setItem('exportHistory', JSON.stringify(newHistory));

    } finally {
      setExportLoading(false);
      setExportProgress(0);
    }
  };

  // Download single file
  const downloadFile = (content, filename) => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Download multiple files as zip
  const downloadMultipleFiles = async (files) => {
    // This would require a zip library like JSZip
    // For now, download the main file
    const mainFile = files['App.jsx'] || files['App.tsx'] || files['index.html'] || Object.values(files)[0];
    if (mainFile) {
      downloadFile(mainFile, 'App.jsx');
    }
  };

  // Download zip file
  const downloadZipFile = (base64Data, filename) => {
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    const blob = new Blob([bytes], { type: 'application/zip' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Get file extension for format
  const getFileExtension = (format) => {
    const extensions = {
      'react': 'jsx',
      'react-ts': 'tsx',
      'vue': 'vue',
      'vue-ts': 'vue',
      'angular': 'ts',
      'svelte': 'svelte',
      'html': 'html',
      'react-native': 'jsx',
      'flutter': 'dart',
      'ionic': 'ts'
    };
    return extensions[format] || 'js';
  };

  // Copy code to clipboard
  const handleCopyCode = () => {
    navigator.clipboard.writeText(codePreview)
      .then(() => {
        message.success('Code copied to clipboard');
      })
      .catch(error => {
        console.error('Failed to copy code:', error);
        message.error('Failed to copy code');
      });
  };

  // Handle template export
  const handleTemplateExport = async (templateId, templateType) => {
    setExportLoading(true);
    setExportProgress(0);

    try {
      const response = await fetch('/api/export-template/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          template_id: templateId,
          template_type: templateType,
          format: exportFormat,
          options: exportOptions
        })
      });

      if (!response.ok) {
        throw new Error('Template export failed');
      }

      const result = await response.json();

      // Handle download
      if (result.type === 'project') {
        await downloadMultipleFiles(result.files);
        message.success('Template exported successfully!');
      }

    } catch (error) {
      console.error('Template export failed:', error);
      message.error('Template export failed. Please try again.');
    } finally {
      setExportLoading(false);
      setExportProgress(0);
    }
  };

  // Handle batch export
  const handleBatchExport = async (appIds) => {
    setExportLoading(true);
    setExportProgress(0);

    try {
      const response = await fetch('/api/batch-export/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          app_ids: appIds,
          format: exportFormat,
          options: exportOptions
        })
      });

      if (!response.ok) {
        throw new Error('Batch export failed');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `batch-export-${exportFormat}.zip`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      message.success('Batch export completed successfully!');

    } catch (error) {
      console.error('Batch export failed:', error);
      message.error('Batch export failed. Please try again.');
    } finally {
      setExportLoading(false);
      setExportProgress(0);
    }
  };

  return (
    <ExporterContainer>
      <Title level={2}>
        <CodeOutlined /> Enhanced Code Exporter
      </Title>
      <Paragraph>
        Export your application to multiple frameworks with advanced configuration options,
        preview functionality, and project management features.
      </Paragraph>

      <Tabs activeKey={activeTab} onChange={setActiveTab} size="large">
        <TabPane tab={<span><SettingOutlined />Configure</span>} key="configure">
          <OptionsGrid>
            {/* Export Format Selection */}
            <OptionCard title="Export Format" size="small">
              <Select
                value={exportFormat}
                onChange={setExportFormat}
                style={{ width: '100%' }}
                size="large"
              >
                {exportFormats.map(format => (
                  <Option key={format.value} value={format.value}>
                    <Space>
                      <span>{format.icon}</span>
                      <div>
                        <div>{format.label}</div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {format.description}
                        </Text>
                      </div>
                    </Space>
                  </Option>
                ))}
              </Select>
            </OptionCard>

            {/* Project Structure */}
            <OptionCard title="Project Structure" size="small">
              <Radio.Group
                value={exportOptions.projectStructure}
                onChange={e => handleOptionChange('projectStructure', e.target.value)}
                style={{ width: '100%' }}
              >
                {projectStructures.map(structure => (
                  <Radio key={structure.value} value={structure.value} style={{ display: 'block', marginBottom: 8 }}>
                    <div>
                      <div>{structure.label}</div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {structure.description}
                      </Text>
                    </div>
                  </Radio>
                ))}
              </Radio.Group>
            </OptionCard>

            {/* Style Framework */}
            <OptionCard title="Style Framework" size="small">
              <Select
                value={exportOptions.styleFramework}
                onChange={value => handleOptionChange('styleFramework', value)}
                style={{ width: '100%' }}
              >
                {styleFrameworks.map(framework => (
                  <Option key={framework.value} value={framework.value}>
                    <div>
                      <div>{framework.label}</div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {framework.description}
                      </Text>
                    </div>
                  </Option>
                ))}
              </Select>
            </OptionCard>

            {/* State Management */}
            <OptionCard title="State Management" size="small">
              <Select
                value={exportOptions.stateManagement}
                onChange={value => handleOptionChange('stateManagement', value)}
                style={{ width: '100%' }}
              >
                {stateManagementOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    <div>
                      <div>{option.label}</div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {option.description}
                      </Text>
                    </div>
                  </Option>
                ))}
              </Select>
            </OptionCard>
          </OptionsGrid>

          {/* Basic Options */}
          <Card title="Basic Options" size="small" style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Checkbox
                checked={exportOptions.typescript}
                onChange={e => handleOptionChange('typescript', e.target.checked)}
              >
                <Space>
                  <span>TypeScript Support</span>
                  <Tooltip title="Generate TypeScript code with type definitions">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              </Checkbox>

              <Checkbox
                checked={exportOptions.includeAccessibility}
                onChange={e => handleOptionChange('includeAccessibility', e.target.checked)}
              >
                <Space>
                  <span>Accessibility Features</span>
                  <Tooltip title="Include ARIA labels, roles, and other accessibility attributes">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              </Checkbox>

              <Checkbox
                checked={exportOptions.includeTests}
                onChange={e => handleOptionChange('includeTests', e.target.checked)}
              >
                <Space>
                  <span>Include Tests</span>
                  <Tooltip title="Generate test files for components">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              </Checkbox>

              <Checkbox
                checked={exportOptions.includeStorybook}
                onChange={e => handleOptionChange('includeStorybook', e.target.checked)}
              >
                <Space>
                  <span>Storybook Stories</span>
                  <Tooltip title="Generate Storybook stories for components">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              </Checkbox>
            </Space>
          </Card>

          {/* Advanced Options */}
          <Collapse>
            <Panel header="Advanced Options" key="advanced">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>Package Manager:</Text>
                  <Radio.Group
                    value={exportOptions.packageManager}
                    onChange={e => handleOptionChange('packageManager', e.target.value)}
                    style={{ marginLeft: 16 }}
                  >
                    <Radio value="npm">npm</Radio>
                    <Radio value="yarn">yarn</Radio>
                    <Radio value="pnpm">pnpm</Radio>
                  </Radio.Group>
                </div>

                <div>
                  <Text strong>Bundler:</Text>
                  <Radio.Group
                    value={exportOptions.bundler}
                    onChange={e => handleOptionChange('bundler', e.target.value)}
                    style={{ marginLeft: 16 }}
                  >
                    <Radio value="vite">Vite</Radio>
                    <Radio value="webpack">Webpack</Radio>
                    <Radio value="parcel">Parcel</Radio>
                  </Radio.Group>
                </div>

                <Checkbox
                  checked={exportOptions.includeDocker}
                  onChange={e => handleOptionChange('includeDocker', e.target.checked)}
                >
                  Include Docker Configuration
                </Checkbox>

                <Checkbox
                  checked={exportOptions.includeCiCd}
                  onChange={e => handleOptionChange('includeCiCd', e.target.checked)}
                >
                  Include CI/CD Pipeline
                </Checkbox>
              </Space>
            </Panel>
          </Collapse>

          <Divider />

          <Space>
            <Button
              type="primary"
              size="large"
              icon={<ExportOutlined />}
              onClick={handleEnhancedExport}
              loading={exportLoading}
            >
              Export Application
            </Button>

            <Button
              size="large"
              icon={<EyeOutlined />}
              onClick={() => setActiveTab('preview')}
            >
              Preview Code
            </Button>
          </Space>

          {exportLoading && (
            <div style={{ marginTop: 16 }}>
              <Progress percent={exportProgress} status="active" />
              <Text type="secondary">Generating your application...</Text>
            </div>
          )}
        </TabPane>

        <TabPane tab={<span><EyeOutlined />Preview</span>} key="preview">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert
              message="Code Preview"
              description="This is a preview of the generated code. Use the Configure tab to adjust export options."
              type="info"
              showIcon
              action={
                <Space>
                  <Button size="small" onClick={generatePreview} loading={previewLoading}>
                    Refresh
                  </Button>
                  <Button size="small" icon={<CopyOutlined />} onClick={handleCopyCode}>
                    Copy
                  </Button>
                </Space>
              }
            />

            <PreviewContainer>
              {previewLoading ? (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                  <Spin size="large" />
                  <div style={{ marginTop: 16 }}>Generating preview...</div>
                </div>
              ) : (
                <CodePreview>{codePreview}</CodePreview>
              )}
            </PreviewContainer>
          </Space>
        </TabPane>

        <TabPane tab={<span><HistoryOutlined />History</span>} key="history">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4}>Export History</Title>
              <Button
                icon={<DeleteOutlined />}
                onClick={() => {
                  setExportHistory([]);
                  localStorage.removeItem('exportHistory');
                  message.success('Export history cleared');
                }}
              >
                Clear History
              </Button>
            </div>

            {exportHistory.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Text type="secondary">No export history yet</Text>
              </div>
            ) : (
              <List
                dataSource={exportHistory}
                renderItem={item => (
                  <List.Item
                    actions={[
                      <Button size="small" icon={<DownloadOutlined />}>
                        Re-export
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <span>{exportFormats.find(f => f.value === item.format)?.label || item.format}</span>
                          <Tag color={item.status === 'success' ? 'green' : 'red'}>
                            {item.status}
                          </Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <div>Exported on {new Date(item.timestamp).toLocaleString()}</div>
                          {item.size && <div>Size: {item.size} characters</div>}
                          {item.error && <div style={{ color: 'red' }}>Error: {item.error}</div>}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </Space>
        </TabPane>
      </Tabs>
    </ExporterContainer>
  );
};

export default EnhancedCodeExporter;
